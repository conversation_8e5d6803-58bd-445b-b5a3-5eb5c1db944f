{"name": "blog-frontend", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.8", "@react-router/node": "^7.7.1", "@react-router/serve": "^7.7.1", "@stomp/stompjs": "^7.1.1", "@tanstack/react-query": "^5.83.0", "@tiptap/extension-code-block-lowlight": "^3.0.9", "@tiptap/extension-color": "^3.0.9", "@tiptap/extension-horizontal-rule": "^3.0.9", "@tiptap/extension-image": "^3.0.9", "@tiptap/extension-text-style": "^3.0.9", "@tiptap/extension-typography": "^3.0.9", "@tiptap/extensions": "^3.0.9", "@tiptap/pm": "^3.0.9", "@tiptap/react": "^3.0.9", "@tiptap/starter-kit": "^3.0.9", "@uiw/react-md-editor": "^4.0.8", "axios": "^1.11.0", "boring-avatars": "^2.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "isbot": "^5.1.27", "lowlight": "^3.3.0", "lucide-react": "^0.539.0", "motion": "^12.23.12", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.61.1", "react-medium-image-zoom": "^5.3.0", "react-router": "^7.7.1", "recharts": "2.15.4", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "zod": "^4.0.13", "zustand": "^5.0.6"}, "devDependencies": {"@react-router/dev": "^7.7.1", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.6", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}